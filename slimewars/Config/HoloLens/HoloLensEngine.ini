

[/Script/HoloLensPlatformEditor.HoloLensTargetSettings]
bBuildForEmulation=False
bBuildForDevice=True
bUseNameForLogo=True
bBuildForRetailWindowsStore=False
bAutoIncrementVersion=False
bShouldCreateAppInstaller=False
AppInstallerInstallationURL=
HoursBetweenUpdateChecks=0
bEnablePIXProfiling=False
TileBackgroundColor=(B=64,G=0,R=0,A=255)
SplashScreenBackgroundColor=(B=64,G=0,R=0,A=255)
+PerCultureResources=(CultureId="",Strings=(PackageDisplayName="",PublisherDisplayName="",PackageDescription="",ApplicationDisplayName="",ApplicationDescription=""),Images=())
TargetDeviceFamily=Windows.Holographic
MinimumPlatformVersion=
MaximumPlatformVersionTested=10.0.19041.0
MaxTrianglesPerCubicMeter=500.000000
SpatialMeshingVolumeSize=20.000000
CompilerVersion=Default
Windows10SDKVersion=10.0.18362.0
+CapabilityList=internetClientServer
+CapabilityList=privateNetworkClientServer
+Uap2CapabilityList=spatialPerception
bSetDefaultCapabilities=False
SpatializationPlugin=
ReverbPlugin=
OcclusionPlugin=
SoundCueCookQualityIndex=-1

