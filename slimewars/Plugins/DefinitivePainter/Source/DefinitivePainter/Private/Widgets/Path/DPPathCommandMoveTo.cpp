// Copyright 2021 <PERSON>. All Rights Reserved.

#include "Widgets/Path/DPPathCommandMoveTo.h"
#include "Container/CommonContainer.h"
#include "Context/DPContext.h"
#include "Widgets/DPWidgetGuidesHelpers.h"

/**
 *
 */
void UDPPathCommandMoveTo::AddToPath(SkPath& Path)
{
	if (RelativeCoordinates)
	{
		Path.rMoveTo(Coords.X, Coords.Y);
	}
	else
	{
		Path.moveTo(Coords.X, Coords.Y);
	}
}

#if WITH_EDITOR
/**
 *
 */
void UDPPathCommandMoveTo::RenderGuides(const UDPContext* Context, const SkPath& Path, const bool DrawGuidelines, const bool DrawGuidePoints) const
{
	SkPoint LastPathPoint;
	Path.getLastPt(&LastPathPoint);

	SkPoint FinalEndPoint = SkPoint::Make(Coords.X, Coords.Y);

	if (RelativeCoordinates)
	{
		FinalEndPoint += LastPathPoint;
	}

	if (DrawGuidelines)
	{
		Context->GetCanvas()->drawLine(LastPathPoint, FinalEndPoint, FCommonContainer::Get()->GetGuidesLineBackgroundPaint());
		Context->GetCanvas()->drawLine(LastPathPoint, FinalEndPoint, FCommonContainer::Get()->GetGuidesLinePaint());
	}

	if (DrawGuidePoints)
	{
		FDPWidgetGuidesHelpers::DrawCenter(Context, FVector2D(FinalEndPoint.x(), FinalEndPoint.y()));
	}
}
#endif