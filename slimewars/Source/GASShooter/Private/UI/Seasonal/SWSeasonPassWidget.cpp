// All rights reserved - Genpop

#include "UI/Seasonal/SWSeasonPassWidget.h"

#include "Features/SWOProgressionSystemSeasonPass.h"

void USWSeasonPassWidget::NativeConstruct()
{
	Super::NativeConstruct();

	if (USWOProgressionSystemSeasonPass* SeasonPassSystem = USWOFeatureSubsystem::Get<USWOProgressionSystemSeasonPass>(GetWorld()))
	{
		TScriptDelegate<> OnSeasonPassFetched;
		OnSeasonPassFetched.BindUFunction(this,"OnSeasonPassFetched");
		SeasonPassSystem->OnSeasonPassFetched.AddUnique(OnSeasonPassFetched);
	}
}

void USWSeasonPassWidget::FetchSeasonPass()
{
	if (USWOProgressionSystemSeasonPass* SeasonPassSystem = USWOFeatureSubsystem::Get<USWOProgressionSystemSeasonPass>(GetWorld()))
	{
		SeasonPassSystem->FetchSeasonPass();
	}
}

FSWOSeasonPassInfo USWSeasonPassWidget::GetCachedSeasonPass()
{
	if (USWOProgressionSystemSeasonPass* SeasonPassSystem = USWOFeatureSubsystem::Get<USWOProgressionSystemSeasonPass>(GetWorld()))
	{
		return SeasonPassSystem->GetCachedSeasonPass();
	}
	return FSWOSeasonPassInfo();
}