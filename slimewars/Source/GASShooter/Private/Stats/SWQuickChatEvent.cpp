// All rights reserved - Genpop


#include "Stats/SWQuickChatEvent.h"
#include "STLibrary.h"


USWQuickChatEvent* USWQuickChatEvent::CommitQuickChatEvent(UObject* WorldContextObject, AActor* SourceActor, FString QuickChatText)
{
	USWQuickChatEvent* Event = USTLibrary::CreateEvent<USWQuickChatEvent>(WorldContextObject, "QuickChat");
	Event->Source = SourceActor;
	Event->QuickChatText = QuickChatText;

	USTLibrary::AddEvent(WorldContextObject, Event);
	return Event;
}

FString USWQuickChatEvent::ToString() const
{
	return FString::Printf(TEXT("%s: %s Sent a Quick Chat saying %s "), *Type.ToString(), *Source.ActorId, *QuickChatText);
}
