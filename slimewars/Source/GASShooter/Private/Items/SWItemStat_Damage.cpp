#include "Items/SWItemStat_Damage.h"
#include "Stats/SWDamageEvent.h"
#include "Items/SWItem.h"

ASWItemStat_Damage::ASWItemStat_Damage()
	: Item(nullptr)
{
	StatType = EAggregationType::Total;
}

void ASWItemStat_Damage::TrackItem(const UObject* TrackedItem, const USWItemStatTrackerConfig* Config)
{
	// Damage needs no config
	Item = TrackedItem;
}

bool ASWItemStat_Damage::IsEventRelevant(const USTEvent* Event) const
{
	const USWDamageEvent* DamageEvent = Cast<USWDamageEvent>(Event);
	if (!DamageEvent)
	{
		return false;
	}
	
	if (DamageEvent->Source.Equals(Actor) && DamageEvent->EffectSpec.GetEffectContext().GetSourceObject() == Item
		&& !DamageEvent->IsSelfDamage() && DamageEvent->IsPlayerDamage())
	{
		return true;
	}

	return false;
}

bool ASWItemStat_Damage::IsSupportedName(FName StatName)
{
	return USWItemStatLibrary::IsSupportedItemStatName(StatName);
}

ASWItemStat_Healing::ASWItemStat_Healing()
	: Item(nullptr)
{
	StatType = EAggregationType::Total;
}

void ASWItemStat_Healing::TrackItem(const UObject* TrackedItem, const USWItemStatTrackerConfig* Config)
{
	// Damage needs no config
	Item = TrackedItem;
}

bool ASWItemStat_Healing::IsEventRelevant(const USTEvent* Event) const
{
	const USWDamageEvent* DamageEvent = Cast<USWDamageEvent>(Event);
	if (!DamageEvent)
	{
		return false;
	}
	
	if (Event->Type == "Heal")
	{
		if (DamageEvent->Target.Equals(Actor) && DamageEvent->EffectSpec.GetEffectContext().GetSourceObject() == Item)
		{
			return true;
		}
	}

	return false;
}

bool ASWItemStat_Healing::IsSupportedName(FName StatName)
{
	return USWItemStatLibrary::IsSupportedItemStatName(StatName);
}
