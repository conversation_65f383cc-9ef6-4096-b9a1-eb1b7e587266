// All rights reserved - Genpop

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "UObject/Object.h"
#include "SWRecoilHandler.generated.h"

class APlayerController;

USTRUCT(BlueprintType)
struct FRecoilData : public FTableRowBase
{
GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Pitch = 0;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Yaw = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float PitchWhileAiming = 0;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float YawWhileAiming = 0;
};

UCLASS(Blueprintable, BlueprintType)
class GASSHOOTER_API USWRecoilHandler : public UObject
{
	GENERATED_BODY()
public:
	
	UFUNCTION(BlueprintCallable, Category = "SlimeWars|Recoil")
	bool StartRecoil(float FireRate,  APlayerController* PlayerController);
	
	UFUNCTION(BlueprintCallable, Category = "SlimeWars|Recoil")
	void EndRecoil();

	UFUNCTION(BlueprintCallable, Category = "SlimeWars|Recoil")
	void OnShotFired();

	UFUNCTION(BlueprintCallable, Category = "SlimeWars|Recoil")
	void OnReload();
	
	UFUNCTION(BlueprintCallable, Category = "SlimeWars|Recoil")
	void SetADSState(bool IsAiming);

	UFUNCTION(BlueprintCallable, Category = "SlimeWars|Recoil")
	bool IsRecoilInitialized();
	
protected:
	void Tick();

	void RecoverRecoil();
	
	void StartRecovery();
	
	void EndRecovery();

	FRotator GetCurrentExpectedTarget();
	
	//Tick Rate of recoil, determines the smoothness of the recoil, lower values increase performance cost though.
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Slimewars|Recoil")
	float TickRate = 0.0069f;

	//Determines the speed of interpolation back, higher values are faster, lower numbers are slower.
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	float RecoverySpeed = 10.0f;

	//Skip trying to recoil or recover on the Yaw axis
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	bool bBypassYaw = false;

	//Skip recovery on the Yaw axis
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	bool bSkipYawRecovery = false;
	
	//The initial delay before recoil starts to decrement itself back through its table and return to the recoil for the first shot.
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	float InitialRecoilRecoveryDelay;

	//Should overrite the firing rate and use value instead of weapon default
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	bool bUseRecoilStartingDelayOverride = false;
	
	//Time to wait between recoil instances
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	float RecoilStartingDelayOverride;
	
	//Divisor to the speed at which each point of recoil is lost, x - x/RecoilRecoverSpeedModifier 
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	float RecoilRecoverySpeedModifier = 2.0f;

	//Should tie the length of time recoil happens in directly to the firing rate of the weapon.
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	bool bUseFireRateForRecoilLength = true;
	//if not tied to fire rate, the length of time that each recoil value should move itself over
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil", meta = (EditCondition = "!bUseFireRateForRecoilLength"))
	float RecoilLengthPerShot = 0.0f;
	//A data table of pitch and yaw values that each shot in a magazine applies
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "SlimeWars|Recoil")
	UDataTable* RecoilDataTable;


	bool bIsRecoiling;
	bool bIsFiring;
	bool bIsRecovering;
	bool bRecoverOnYaw;
	bool bIsInitialized;
	
	float TimeSinceRecoilStarted;
	float TimeSinceLastShot;
	float CurrentRecoilRecoveryTime;
	float CurrentRecoveryTime;
	
	int32 CurrentShotIndex;
	
	FRotator TargetRotation;
	FRotator CurrentRotation;

	float RecoilRecoveryStartingDelay;
	float RecoilLength;
	
	FTimerHandle TickTimer;
	FTimerHandle RecoilRecoveryTimer;

	APlayerController* PlayerControllerReference;
	
	FRotator OriginalControlRotation;

	bool bIsAiming;
};
