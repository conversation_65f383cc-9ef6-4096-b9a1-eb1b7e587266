// All rights reserved - <PERSON>pop

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "SWHUDLibrary.generated.h"

class UAbilitySystemComponent;
class USWGameplayAbility;

UENUM(BlueprintType)
enum class ESWCooldownState : uint8
{
	Ready,	
	Charge,
	Cooldown,
	Intermission,
};

USTRUCT(BlueprintType)
struct FAbilityCooldownState
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	ESWCooldownState State = ESWCooldownState::Ready;

	// The remaining cooldown time for the current cooldown state
	UPROPERTY(BlueprintReadWrite)
	float CooldownTime = 0;

	// The max duration of the cooldown for the current cooldown state
	UPROPERTY(BlueprintReadWrite)
	float CooldownDuration = 0;

	// THe maximum number of charges this ability supports
	UPROPERTY(BlueprintReadWrite)
	int32 MaxCharges = 0;

	// How many charges the ability has remaining
	UPROPERTY(BlueprintReadWrite)
	int32 CurrentCharges = 0;
};

/**
 * 
 */
UCLASS()
class GASSHOOTER_API USWHUDLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable, Category = "SlimeWars|HUD|Cooldown")
	static FAbilityCooldownState GetAbilityCooldownState(const UAbilitySystemComponent* ASC, const USWGameplayAbility* Ability);
};
