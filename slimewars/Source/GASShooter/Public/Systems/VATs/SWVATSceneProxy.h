#pragma once

#include "PrimitiveSceneProxy.h"
#include "VertexFactory.h"
#include "RenderResource.h"
#include "Materials/Material.h"

#include "SWShaders/VATs/SWVATVertexFactory.h"

class USWVATMeshComponent;
class USWVATAsset;
class USWVLUTAsset;

class FSWVATSceneProxy : public FPrimitiveSceneProxy
{
public:
	USWVLUTAsset* VLUT = nullptr;
	USWVATAsset* VAT = nullptr;
	int Frame = 0;
	int VLUTWidth = 0;
	int VATWidth = 0;
	int VATHeight = 0;
	float VATScale = 1000.f;

public:
    FSWVATSceneProxy(
		USWVATMeshComponent* Component,
		USWVLUTAsset* InVLUT,
		USWVATAsset* InVAT,
		int32 InFrame,
		int32 InVLUTWidth,
		int32 InVATWidth,
		int32 InVATHeight,
		float InWPOAmount
	);
    virtual ~FSWVATSceneProxy();

    virtual void GetDynamicMeshElements(const TArray<const FSceneView*>& Views,
                                        const FSceneViewFamily& ViewFamily,
                                        uint32 VisibilityMap,
                                        FMeshElementCollector& Collector) const override;

    virtual FPrimitiveViewRelevance GetViewRelevance(const FSceneView* View) const override;
    virtual uint32 GetMemoryFootprint() const override;
    SIZE_T GetTypeHash() const override;

	const FBoxSphereBounds& GetBounds() const { return VATBounds; }
	FVATVertexFactory& GetVertexFactory() { return VertexFactory; }

private:
	FBoxSphereBounds VATBounds;

    UMaterialInterface* Material;
    FMaterialRelevance MaterialRelevance;

    //TArray<FSimpleVertex> Vertices;
    //TArray<uint32> Indices;

    //FSimpleVertexBuffer VertexBuffer;
    //FSimpleIndexBuffer IndexBuffer;
	FVATVertexFactory VertexFactory;
};
