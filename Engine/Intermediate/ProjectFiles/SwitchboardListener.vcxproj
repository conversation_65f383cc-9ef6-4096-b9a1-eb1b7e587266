<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Invalid|x64">
      <Configuration>Invalid</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug_Program|x64">
      <Configuration>Debug_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Debug_Program|x64">
      <Configuration>Win64_arm64_Debug_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Debug_Program|x64">
      <Configuration>Win64_arm64ec_Debug_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Linux_Debug_Program|x64">
      <Configuration>Linux_Debug_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="LinuxArm64_Debug_Program|x64">
      <Configuration>LinuxArm64_Debug_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Program|x64">
      <Configuration>DebugGame_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_DebugGame_Program|x64">
      <Configuration>Win64_arm64_DebugGame_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_DebugGame_Program|x64">
      <Configuration>Win64_arm64ec_DebugGame_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Linux_DebugGame_Program|x64">
      <Configuration>Linux_DebugGame_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="LinuxArm64_DebugGame_Program|x64">
      <Configuration>LinuxArm64_DebugGame_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Program|x64">
      <Configuration>Development_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Development_Program|x64">
      <Configuration>Win64_arm64_Development_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Development_Program|x64">
      <Configuration>Win64_arm64ec_Development_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Linux_Development_Program|x64">
      <Configuration>Linux_Development_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="LinuxArm64_Development_Program|x64">
      <Configuration>LinuxArm64_Development_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Test_Program|x64">
      <Configuration>Test_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Test_Program|x64">
      <Configuration>Win64_arm64_Test_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Test_Program|x64">
      <Configuration>Win64_arm64ec_Test_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Linux_Test_Program|x64">
      <Configuration>Linux_Test_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="LinuxArm64_Test_Program|x64">
      <Configuration>LinuxArm64_Test_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Program|x64">
      <Configuration>Shipping_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Shipping_Program|x64">
      <Configuration>Win64_arm64_Shipping_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Shipping_Program|x64">
      <Configuration>Win64_arm64ec_Shipping_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Linux_Shipping_Program|x64">
      <Configuration>Linux_Shipping_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="LinuxArm64_Shipping_Program|x64">
      <Configuration>LinuxArm64_Shipping_Program</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9CC4D316-80FB-3781-A414-5CB1EF396605}</ProjectGuid>
    <RootNamespace>SwitchboardListener</RootNamespace>
  </PropertyGroup>
  <Import Project="UECommon.props" />
  <ImportGroup Label="ExtensionSettings" />
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <NMakePreprocessorDefinitions>$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <IncludePath>$(IncludePath);..\Build\Win64\SwitchboardListener\Inc\ApplicationCore\UHT;..\Build\Win64\SwitchboardListener\Inc\ApplicationCore\VNI;..\Build\Win64\SwitchboardListener\Inc\CoreUObject\UHT;..\Build\Win64\SwitchboardListener\Inc\CoreUObject\VerseVMBytecode;..\Build\Win64\SwitchboardListener\Inc\CoreUObject\VNI;..\Build\Win64\SwitchboardListener\Inc\Core\UHT;..\Build\Win64\SwitchboardListener\Inc\Core\VNI;..\Build\Win64\SwitchboardListener\Inc\Json\UHT;..\Build\Win64\SwitchboardListener\Inc\Json\VNI;..\Build\Win64\SwitchboardListener\Inc\NetCommon\UHT;..\Build\Win64\SwitchboardListener\Inc\NetCommon\VNI;..\Build\Win64\SwitchboardListener\Inc\Networking\UHT;..\Build\Win64\SwitchboardListener\Inc\Networking\VNI;..\Build\Win64\SwitchboardListener\Inc\Projects\UHT;..\Build\Win64\SwitchboardListener\Inc\Projects\VNI;..\Build\Win64\SwitchboardListener\Inc\RHI\UHT;..\Build\Win64\SwitchboardListener\Inc\RHI\VNI;..\Build\Win64\SwitchboardListener\Inc\SblCore\UHT;..\Build\Win64\SwitchboardListener\Inc\SblCore\VNI;..\Build\Win64\SwitchboardListener\Inc\Sockets\UHT;..\Build\Win64\SwitchboardListener\Inc\Sockets\VNI;..\Build\Win64\SwitchboardListener\Inc\SwitchboardCommon\UHT;..\Build\Win64\SwitchboardListener\Inc\SwitchboardCommon\VNI;..\Build\Win64\SwitchboardListener\Inc\TraceLog\UHT;..\Build\Win64\SwitchboardListener\Inc\TraceLog\VNI;..\..\Plugins\VirtualProduction\Switchboard\Source;..\..\Plugins\VirtualProduction\Switchboard\Source\SwitchboardCommon\Internal;..\..\Source;..\..\Source\Programs\SwitchboardListener\SblCore\Internal;..\..\Source\Programs\SwitchboardListener\SblCore\NvmlWrapper\Public;..\..\Source\Runtime\ApplicationCore\Public;..\..\Source\Runtime\CoreUObject\Internal;..\..\Source\Runtime\CoreUObject\Public;..\..\Source\Runtime\Core\Internal;..\..\Source\Runtime\Core\Public;..\..\Source\Runtime\Json\Public;..\..\Source\Runtime\Networking\Public;..\..\Source\Runtime\Net\Common\Public;..\..\Source\Runtime\Projects\Internal;..\..\Source\Runtime\Projects\Public;..\..\Source\Runtime\RHI\Public;..\..\Source\Runtime\Sockets\Public;..\..\Source\Runtime\TraceLog\Public;..\..\Source\ThirdParty\MsQuic\v220\win64\include;..\..\Source\ThirdParty\OpenSSL\1.1.1t\include\Win64\VS2015;..\Build\Win64\SwitchboardListener\Inc\Launch\UHT;..\Build\Win64\SwitchboardListener\Inc\Launch\VNI;..\Build\Win64\SwitchboardListener\Inc\ProfilerService\UHT;..\Build\Win64\SwitchboardListener\Inc\ProfilerService\VNI;..\..\Source\Developer\ProfilerService\Public;..\..\Source\Runtime\Launch\Public;..\Build\Win64\SwitchboardListener\Inc\JsonUtilities\UHT;..\Build\Win64\SwitchboardListener\Inc\JsonUtilities\VNI;..\Build\Win64\SwitchboardListener\Inc\JWT\UHT;..\Build\Win64\SwitchboardListener\Inc\JWT\VNI;..\Build\Win64\SwitchboardListener\Inc\MsQuicRuntime\UHT;..\Build\Win64\SwitchboardListener\Inc\MsQuicRuntime\VNI;..\Build\Win64\SwitchboardListener\Inc\PlatformCryptoOpenSSL\UHT;..\Build\Win64\SwitchboardListener\Inc\PlatformCryptoOpenSSL\VNI;..\Build\Win64\SwitchboardListener\Inc\PlatformCryptoTypes\UHT;..\Build\Win64\SwitchboardListener\Inc\PlatformCryptoTypes\VNI;..\Build\Win64\SwitchboardListener\Inc\PlatformCrypto\UHT;..\Build\Win64\SwitchboardListener\Inc\PlatformCrypto\VNI;..\..\Plugins\Experimental\JWT\Source;..\..\Plugins\Experimental\JWT\Source\JWT\Public;..\..\Plugins\Experimental\PlatformCrypto\Source;..\..\Plugins\Experimental\PlatformCrypto\Source\PlatformCryptoOpenSSL\Public;..\..\Plugins\Experimental\PlatformCrypto\Source\PlatformCryptoTypes\Public;..\..\Plugins\Experimental\PlatformCrypto\Source\PlatformCrypto\Public;..\..\Plugins\Runtime\MsQuic\Source;..\..\Plugins\Runtime\MsQuic\Source\Public;..\..\Source\Programs\SwitchboardListener\SblCore\Private;..\..\Source\Runtime\JsonUtilities\Public;..\..\Source\ThirdParty\NVIDIA\nvapi;..\Build\Win64\SwitchboardListener\Inc\DeveloperSettings\UHT;..\Build\Win64\SwitchboardListener\Inc\DeveloperSettings\VNI;..\Build\Win64\SwitchboardListener\Inc\InputCore\UHT;..\Build\Win64\SwitchboardListener\Inc\InputCore\VNI;..\Build\Win64\SwitchboardListener\Inc\SblSlate\UHT;..\Build\Win64\SwitchboardListener\Inc\SblSlate\VNI;..\Build\Win64\SwitchboardListener\Inc\SlateCore\UHT;..\Build\Win64\SwitchboardListener\Inc\SlateCore\VNI;..\..\Source\Programs\SwitchboardListener\SblSlate\Internal;..\..\Source\Runtime\DeveloperSettings\Public;..\..\Source\Runtime\InputCore\Classes;..\..\Source\Runtime\InputCore\Public;..\..\Source\Runtime\SlateCore\Public;..\Build\Win64\SwitchboardListener\Inc\ImageCore\UHT;..\Build\Win64\SwitchboardListener\Inc\ImageCore\VNI;..\Build\Win64\SwitchboardListener\Inc\ImageWrapper\UHT;..\Build\Win64\SwitchboardListener\Inc\ImageWrapper\VNI;..\Build\Win64\SwitchboardListener\Inc\OutputLog\UHT;..\Build\Win64\SwitchboardListener\Inc\OutputLog\VNI;..\Build\Win64\SwitchboardListener\Inc\SlateReflector\UHT;..\Build\Win64\SwitchboardListener\Inc\SlateReflector\VNI;..\Build\Win64\SwitchboardListener\Inc\Slate\UHT;..\Build\Win64\SwitchboardListener\Inc\Slate\VNI;..\Build\Win64\SwitchboardListener\Inc\StandaloneRenderer\UHT;..\Build\Win64\SwitchboardListener\Inc\StandaloneRenderer\VNI;..\Build\Win64\SwitchboardListener\Inc\SwitchboardListener\UHT;..\Build\Win64\SwitchboardListener\Inc\SwitchboardListener\VNI;..\Build\Win64\SwitchboardListener\Inc\ToolMenus\UHT;..\Build\Win64\SwitchboardListener\Inc\ToolMenus\VNI;..\..\Source\Developer\OutputLog\Public;..\..\Source\Developer\SlateReflector\Public;..\..\Source\Developer\StandaloneRenderer\Public;..\..\Source\Developer\ToolMenus\Public;..\..\Source\Programs\SwitchboardListener\SblSlate\Private;..\..\Source\Programs\SwitchboardListener\SwitchboardListener\Private;..\..\Source\Runtime\ImageCore\Public;..\..\Source\Runtime\ImageWrapper\Public;..\..\Source\Runtime\Slate\Public;..\..\Source\ThirdParty\LibTiff\Source;..\..\Source\ThirdParty\LibTiff\Source\Win64;$(DefaultSystemIncludePaths);</IncludePath>
    <NMakeForcedIncludes>$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath>$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <AdditionalOptions>/std:c++20  /DSAL_NO_ATTRIBUTE_DECLARATIONS=1 /permissive- /Zc:strictStrings- /Zc:__cplusplus</AdditionalOptions>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Debug.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Debug_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Debugarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Debug_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Debug_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Debugarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Debug_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Debug -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Debug_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Linux Debug -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Linux Debug -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Linux Debug -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Linux\SwitchboardListener-Linux-Debug</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Debug_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Linux Debug -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Debug_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Debug -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener LinuxArm64 Debug -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener LinuxArm64 Debug -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\LinuxArm64\SwitchboardListener-LinuxArm64-Debug</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Debug_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Debug -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_DebugGame_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-DebugGamearm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_DebugGame_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_DebugGame_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-DebugGamearm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_DebugGame_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 DebugGame -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Linux_DebugGame_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Linux DebugGame -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Linux DebugGame -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Linux DebugGame -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Linux\SwitchboardListener-Linux-DebugGame</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Linux_DebugGame_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Linux DebugGame -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_DebugGame_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 DebugGame -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener LinuxArm64 DebugGame -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener LinuxArm64 DebugGame -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\LinuxArm64\SwitchboardListener-LinuxArm64-DebugGame</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_DebugGame_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 DebugGame -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Development_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListenerarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Development_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Development_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListenerarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Development_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Development -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Development_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Linux Development -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Linux Development -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Linux Development -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Linux\SwitchboardListener</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Development_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Linux Development -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Development_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Development -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener LinuxArm64 Development -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener LinuxArm64 Development -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\LinuxArm64\SwitchboardListener</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Development_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Development -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Test_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Test.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Test_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Test_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Testarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Test_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Test_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Testarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Test_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Test -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Test_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Linux Test -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Linux Test -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Linux Test -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Linux\SwitchboardListener-Linux-Test</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Test_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Linux Test -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Test_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Test -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener LinuxArm64 Test -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener LinuxArm64 Test -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\LinuxArm64\SwitchboardListener-LinuxArm64-Test</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Test_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Test -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Shipping.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Shipping_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Shippingarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Shipping_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Shipping_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\SwitchboardListener-Win64-Shippingarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Shipping_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Win64 Shipping -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Shipping_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener Linux Shipping -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener Linux Shipping -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener Linux Shipping -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Linux\SwitchboardListener-Linux-Shipping</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Linux_Shipping_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener Linux Shipping -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Shipping_Program|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Shipping -WaitMutex -FromMsBuild</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) SwitchboardListener LinuxArm64 Shipping -WaitMutex -FromMsBuild</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) SwitchboardListener LinuxArm64 Shipping -WaitMutex -FromMsBuild</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\LinuxArm64\SwitchboardListener-LinuxArm64-Shipping</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='LinuxArm64_Shipping_Program|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) SwitchboardListener LinuxArm64 Shipping -WaitMutex -FromMsBuild -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup>
    <ProjectForcedIncludeFiles>$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\CoreUObject\SharedPCH.CoreUObject.Project.ValApi.Cpp20.h</ProjectForcedIncludeFiles>
    <ClCompile_ForcedIncludeFiles>$(ProjectForcedIncludeFiles);$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\SblCore\Definitions.SblCore.h</ClCompile_ForcedIncludeFiles>
    <ClCompile_ForcedIncludeFiles_1>$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\Slate\SharedPCH.Slate.Project.ValApi.Cpp20.h;$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\SblSlate\Definitions.SblSlate.h</ClCompile_ForcedIncludeFiles_1>
    <ClCompile_ForcedIncludeFiles_2>$(ProjectForcedIncludeFiles);$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\SwitchboardListener\Definitions.SwitchboardListener.h</ClCompile_ForcedIncludeFiles_2>
    <ClCompile_AdditionalOptions>$(AdditionalOptions) /Yu"$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\CoreUObject\SharedPCH.CoreUObject.Project.ValApi.Cpp20.h"</ClCompile_AdditionalOptions>
    <ClCompile_AdditionalOptions_1>$(AdditionalOptions) /Yu"$(SolutionDir)Engine\Intermediate\Build\Win64\x64\SwitchboardListenerGPF\Development\Slate\SharedPCH.Slate.Project.ValApi.Cpp20.h"</ClCompile_AdditionalOptions_1>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListener.Target.cs"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SblCore\SblCore.Build.cs"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\CpuUtilizationMonitor.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardAuth.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardCredential.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardCredentialInterface.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardListener.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardListenerApp.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardListenerMainCommon.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\SwitchboardListenerVersion.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\Linux\LinuxSwitchboardCredential.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\PosixOS\PosixOSSwitchboardCredential.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\Windows\WindowsCpuUtilizationMonitor.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Internal\Windows\WindowsSwitchboardCredential.h"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SblCore\NvmlWrapper\Lib\Linux\libNvmlWrapper.a"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SblCore\NvmlWrapper\Lib\Windows\NvmlWrapper.lib"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\NvmlWrapper\Public\GpuClocker.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\NvmlWrapper\Public\NvmlWrapperPublic.h"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\CpuUtilizationMonitor.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SBLHelperClient.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SBLHelperClient.h"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\ScopedNvApi.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\ScopedNvApi.h"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardAuth.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardListener.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardMainCommon.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardMessageFuture.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardPacket.h"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardProtocol.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardProtocol.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SwitchboardTasks.h"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\SyncStatus.h"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\PosixOS\PosixOSSwitchboardCredential.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\Windows\WindowsCpuUtilizationMonitor.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\Windows\WindowsSwitchboardCredential.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\Windows\Resources\Icon.ico"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\Windows\Resources\Resource.h"/>
    <ResourceCompile Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\Windows\Resources\Resource.rc"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SblCore\Private\Windows\Resources\Resource.rc2"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SblSlate\SblSlate.Build.cs"/>
    <ClInclude Include="..\..\Source\Programs\SwitchboardListener\SblSlate\Internal\SblMainWindow.h"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SblSlate\Private\SblMainWindow.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListenerCmd\SwitchboardListenerCmd.Build.cs"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListenerCmd\Private\SwitchboardListenerCmdMain.cpp"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListenerTests\SwitchboardListenerTests.Build.cs"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListenerTests\Private\SwitchboardAuthTests.cpp"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListenerTests\Private\SwitchboardCredentialTests.cpp"/>
    <None Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListener\SwitchboardListener.Build.cs"/>
    <ClCompile Include="..\..\Source\Programs\SwitchboardListener\SwitchboardListener\Private\SwitchboardListenerMain.cpp">
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
  </ItemGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <PropertyGroup>
    <CleanDependsOn> $(CleanDependsOn); </CleanDependsOn>
    <CppCleanDependsOn></CppCleanDependsOn>
  </PropertyGroup>
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
