// Copyright Epic Games, Inc. All Rights Reserved.

float SampleBiCubic_{ParameterName}(SamplerState Sampler, float3 UVW, int MipLevel)
{		    
	const int AttributeIndex = round(UVW[2]);

	const float2 GridPos = UVW.xy * {NumCellsName}.xy - .5;
								
	// identify the lower-left-hand corner of the cell
	const int2 GridCell = floor(GridPos);
								
	const int2 MaxCell = {NumCellsName} - int2(2,2);

	if (any((GridCell < int2(1,1)) || (GridCell >= MaxCell)))
	{
		// revert to bilinear hardware sampling at the boundary cells.
		return {GridName}.SampleLevel(Sampler, UVW, MipLevel);
	}
	else 
	{
		// sample point offset from lower left
		float2 delta = GridPos - GridCell;
		
		float4x2 Weights;

		Weights[0] = -1./3 * delta + 1./2 * delta * delta - 1./6 * delta * delta * delta;
		Weights[1] = 1. - delta * delta + 1./2 * (delta * delta * delta - delta);
		Weights[2] = delta + 1./2 * (delta * delta - delta * delta * delta);
		Weights[3] = 1./6 * (delta * delta * delta - delta);		

		float minv = 3.402823466e+38;
		float maxv = -3.402823466e+38;

		float BiCubicValue = 0;		
		for (int j = 0; j < 4; ++j)
		{
			float qj = 0;

			for (int i = 0; i < 4; ++i)
			{
				float Val = {GridName}.Load(int4(GridCell.x + i - 1, GridCell.y + j - 1, AttributeIndex, MipLevel)); 
				qj += Weights[i][0] * Val;

				minv = min(Val, minv);
				maxv = max(Val, maxv);
			}

			BiCubicValue += Weights[j][1] * qj;
		}

		return min(max(BiCubicValue, minv), maxv);
	}
}