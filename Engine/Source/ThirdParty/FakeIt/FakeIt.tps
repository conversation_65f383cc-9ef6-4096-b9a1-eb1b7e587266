<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>FakeIt</Name>
  <Location>/Engine/Source/ThirdParty/FakeIt/</Location>
  <Date>2016-06-09T17:06:56.3768591-04:00</Date>
  <Function>It allows the programmer to create a light-weight 'mock' implementation of an interface or object which can be used in writing unit tests.</Function>
  <Justification>For robust unit testing.</Justification>
  <Eula>https://github.com/eranpeer/FakeIt/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>